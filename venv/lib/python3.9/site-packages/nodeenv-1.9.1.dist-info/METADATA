Metadata-Version: 2.1
Name: nodeenv
Version: 1.9.1
Summary: Node.js virtual environment builder
Home-page: https://github.com/ekalinin/nodeenv
Author: <PERSON>
Author-email: e.v.ka<PERSON><PERSON>@gmail.com
License: BSD
Platform: any
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*
License-File: LICENSE
License-File: AUTHORS

Node.js virtual environment
===========================

``nodeenv`` (node.js virtual environment) is a tool to create 
isolated node.js environments.

It creates an environment that has its own installation directories, 
that doesn't share libraries with other node.js virtual environments.

Also the new environment can be integrated with the environment which was built
by virtualenv_ (python).

If you use nodeenv feel free to add your project on wiki: `Who-Uses-Nodeenv`_.

.. _Who-Uses-Nodeenv: https://github.com/ekalinin/nodeenv/wiki/Who-Uses-Nodeenv

.. image:: https://travis-ci.org/ekalinin/nodeenv.svg?branch=master
    :target: https://travis-ci.org/ekalinin/nodeenv

.. contents:: :local:


Install
-------

Global installation
^^^^^^^^^^^^^^^^^^^

You can install nodeenv globally with `easy_install`_::

    $ sudo easy_install nodeenv

or with `pip`_::

    $ sudo pip install nodeenv

or on Debian using `dpkg`_::

    $ ln -s debian-upstream debian
    $ dpkg-buildpackage -uc -us -b
    $ sudo dpkg -i $(ls -1rt ../nodeenv_*.deb | tail -n1)

.. _dpkg: https://www.debian.org/doc/manuals/debian-faq/ch-pkgtools.en.html

Local installation
^^^^^^^^^^^^^^^^^^

If you're using virtualenv_ then you can install nodeenv via
pip_/easy_install_ inside any virtual environment built with virtualenv::

    $ virtualenv env
    $ . env/bin/activate
    (env) $ pip install nodeenv
    (env) $ nodeenv --version
    0.6.5

If you want to work with the latest version of the nodeenv you can 
install it from the github `repository`_::

    $ git clone https://github.com/ekalinin/nodeenv.git
    $ ./nodeenv/nodeenv.py --help

or with `pip`_::

    $ pip install -e git+https://github.com/ekalinin/nodeenv.git#egg=nodeenv

.. _repository: https://github.com/ekalinin/nodeenv
.. _pip: http://pypi.python.org/pypi/pip
.. _easy_install: http://pypi.python.org/pypi/setuptools


Dependency
----------

For nodeenv
^^^^^^^^^^^

* python (2.6+, 3.5+, or pypy)
* make
* tail

For node.js
^^^^^^^^^^^

* libssl-dev

Usage
-----

Basic
^^^^^

Create new environment::

    $ nodeenv env

Activate new environment::

    $ . env/bin/activate

Check versions of main packages::

    (env) $ node -v
    v0.10.26

    (env) $ npm -v
    1.4.3

Deactivate environment::

    (env) $ deactivate_node

Advanced
^^^^^^^^

Get available node.js versions::

    $ nodeenv --list
    0.0.1   0.0.2   0.0.3   0.0.4   0.0.5   0.0.6   0.1.0
    0.1.2   0.1.3   0.1.4   0.1.5   0.1.6   0.1.7   0.1.8
    0.1.10  0.1.11  0.1.12  0.1.13  0.1.14  0.1.15  0.1.16
    0.1.18  0.1.19  0.1.20  0.1.21  0.1.22  0.1.23  0.1.24
    0.1.26  0.1.27  0.1.28  0.1.29  0.1.30  0.1.31  0.1.32
    0.1.90  0.1.91  0.1.92  0.1.93  0.1.94  0.1.95  0.1.96
    0.1.98  0.1.99  0.1.100 0.1.101 0.1.102 0.1.103 0.1.104
    0.2.1   0.2.2   0.2.3   0.2.4   0.2.5   0.2.6   0.3.0
    0.3.2   0.3.3   0.3.4   0.3.5   0.3.6   0.3.7   0.3.8
    0.4.1   0.4.2   0.4.3   0.4.4   0.4.5   0.4.6

Install node.js "0.4.3" without ssl support with 4 parallel commands 
for compilation and npm.js "0.3.17"::

    $ nodeenv --without-ssl --node=0.4.3 --npm=0.3.17 --with-npm --jobs=4 env-4.3

Install node.js from the source::

    $ nodeenv --node=0.10.25 --source env-0.10.25

Install node.js from a mirror::

    $ nodeenv --node=10.19.0 --mirror=https://npm.taobao.org/mirrors/node

It's much faster to install from the prebuilt package than Install & compile
node.js from source::

    $ time nodeenv --node=0.10.25 --prebuilt env-0.10.25-prebuilt
     + Install node.js (0.10.25) ... done.

    real    0m6.928s
    user    0m0.408s
    sys     0m1.144s

    $ time nodeenv --node=0.10.25 --source env-0.10.25-src
     + Install node.js (0.10.25) ... done.

    real    4m12.602s
    user    6m34.112s
    sys     0m30.524s

Create a new environment with the system-wide node.js::

    $ nodeenv --node=system

Saving the versions of all installed packages to a file::

    $ . env-4.3/bin/activate
    (env-4.3)$ npm install -g express
    (env-4.3)$ npm install -g jade
    (env-4.3)$ freeze ../prod-requirements.txt

If you want to list locally installed packages use ``-l`` option::

    (env-4.3)$ freeze -l ../prod-requirements.txt

Create an environment from a requirements file::

    $ nodeenv --requirements=../prod-requirements.txt --jobs=4 env-copy

Requirements files are plain text files that contain a list of packages 
to be installed. These text files allow you to create repeatable installations.
Requirements file example::

    $ cat ../prod-requirements.txt
    connect@1.3.0
    express@2.2.2
    jade@0.10.4
    mime@1.2.1
    npm@0.3.17
    qs@0.0.7

If you already have the python virtualenv tool, and want to use nodeenv and
virtualenv in conjunction, then you should create (or activate) the python
virtual environment::

    # in case of using virtualenv_wrapper
    $ mkvirtualenv my_env

    # in case of using virtualenv
    $ . my_env/bin/activate

and add a node virtual environment to this existing new_venv::

    $ nodeenv -p
    
If you need to set the path to make used to build node::

	$ nodeenv -m /usr/local/bin/gmake ENV

That's all. Now, all your node.js modules will be installed into your virtual
environment::

    $ workon my_env
    $ npm install -g coffee-script
    $ command -v coffee
    /home/<USER>/virtualenvs/my_env/bin/coffee
    
Creating a virtual environment with a custom prompt:

    $ nodeenv --node=12.18.2 --prompt="(myenv)" nodeenv

If environment's directory already exists then you can use ``--force`` option::

    $ nodeenv --requirements=requirements.txt --jobs=4 --force env

If you already have an environment and want to update packages from requirements
file you can use ``--update`` option::

    $ . env-4.3/bin/activate
    (env-4.3)$ nodeenv --requirements=requirements.txt --update env-4.3

If you want to call node from environment without activation then you should
use `shim` script::

    $ ./env-4.3/bin/shim --version
    v0.4.3

Configuration
-------------
You can use the INI-style file ``~/.nodeenvrc`` to set default values for many options,
the keys in that file are the long command-line option names.

These are the available options and their defaults::

    [nodeenv]
    node = 'latest'
    npm = 'latest'
    with_npm = False
    jobs = '2'
    without_ssl = False
    debug = False
    profile = False
    make = 'make'
    prebuilt = True
    ignore_ssl_certs = False
    mirror = None

Alternatives
------------

There are several alternatives that create isolated environments:

* `nave <https://github.com/isaacs/nave>`_ - Virtual Environments for Node.
  Nave stores all environments in one directory ``~/.nave``. Can create
  per node version environments using `nave use envname versionname`.
  Can not pass additional arguments into configure (for example --without-ssl)
  Can't run on windows because it relies on bash.

* `nvm <https://github.com/creationix/nvm/blob/master/nvm.sh>`_ - Node Version
  Manager. It is necessarily to do `nvm sync` for caching available node.js
  version.
  Can not pass additional arguments into configure (for example --without-ssl)

* virtualenv_ - Virtual Python Environment builder. For python only.

.. _`virtualenv`: https://github.com/pypa/virtualenv

LICENSE
=======

BSD / `LICENSE <https://github.com/ekalinin/nodeenv/blob/master/LICENSE>`_


Nodeenv changelog
=================

Version 1.3.1
-------------
- Windows related fix `#207 <https://github.com/ekalinin/nodeenv/pull/207>`_
- Fixed url for arm64 `#210 <https://github.com/ekalinin/nodeenv/pull/210>`_
- Fixed fish support `#212 <https://github.com/ekalinin/nodeenv/pull/212>`_


Version 1.3.0
-------------
- Fixed symlink creation `#189 <https://github.com/ekalinin/nodeenv/issues/189>`_
- Python3.6 support `#200 <https://github.com/ekalinin/nodeenv/pull/200>`_
- Added `activate` for fish `#201 <https://github.com/ekalinin/nodeenv/pull/201>`_
- Fixed cp866 `#202 <https://github.com/ekalinin/nodeenv/pull/202>`_
- Added Conda support `#203 <https://github.com/ekalinin/nodeenv/pull/203>`_


Version 1.2.0
-------------
- Support for Cygwin `#194 <https://github.com/ekalinin/nodeenv/pull/194>`_ `#195
  <https://github.com/ekalinin/nodeenv/pull/195>`_
- tox.ini as default configuration file `#197
  <https://github.com/ekalinin/nodeenv/pull/197>`_

Version 1.1.4
-------------
- Fixed directory copy `#188 <https://github.com/ekalinin/nodeenv/issues/188>`_

Version 1.1.3
-------------
- Fixed spaces in paths `#187 <https://github.com/ekalinin/nodeenv/issues/187>`_

Version 1.1.2
-------------
- Fixed MANIFEST.in `#184 <https://github.com/ekalinin/nodeenv/issues/184>`_

Version 1.1.1
-------------
- Improve Windows support. See `#181
  <https://github.com/ekalinin/nodeenv/pull/181>`_
- Fix bug when downgrading using `--force`. See `#183
  <https://github.com/ekalinin/nodeenv/pull/183>`_
- Environment creation fails with non-ASCII chars in path. See `#49
  <https://github.com/ekalinin/nodeenv/issues/49>`_

Version 1.1.0
-------------
- Windows support

Version 1.0.0
-------------
- `--prebuilt` is default. See `# 161`_
- Added `--source` option
- Added support for the `ARM`. See `# 171`_
- Fixed issue with `$PATH`. See `# 86`_

.. _# 171: https://github.com/ekalinin/nodeenv/issues/171
.. _# 161: https://github.com/ekalinin/nodeenv/issues/161
.. _# 86: https://github.com/ekalinin/nodeenv/issues/86

Version 0.13.6
--------------
- Use https for nodejs.org. See `# 129`_

.. _# 129: https://github.com/ekalinin/nodeenv/issues/129

Version 0.13.5
--------------
- Improved user-agent identification

Version 0.13.4
--------------
- Custom ``user-agent``. See `# 125`_, `# 127`_

.. _# 125: https://github.com/ekalinin/nodeenv/issues/125
.. _# 127: https://github.com/ekalinin/nodeenv/issues/127


Version 0.13.3
--------------
- Fixed env creation with non-ASCII chars in path. See `# 49`_
- Fixed logging with unicode chars. See `# 96`_
- Skip new lines at the end of requirements.txt. See `# 122`_ 
- Fixed UnicodeDecodeError at build on non UTF-8 environment. See `# 124`_

.. _# 49: https://github.com/ekalinin/nodeenv/issues/49
.. _# 96: https://github.com/ekalinin/nodeenv/issues/96
.. _# 122: https://github.com/ekalinin/nodeenv/issues/122
.. _# 124: https://github.com/ekalinin/nodeenv/pull/124

Version 0.13.2
--------------
- Fixed ``freeze`` command. See `# 121`_

.. _# 121: https://github.com/ekalinin/nodeenv/issues/121

Version 0.13.1
--------------
- Fixed bug: ``nodeenv --list is raising TypeError``. See `# 117`_, `# 118`_

.. _# 117: https://github.com/ekalinin/nodeenv/issues/117
.. _# 118: https://github.com/ekalinin/nodeenv/pull/118

Version 0.13.0
--------------
- Removed deps from ``sort``, ``egrep``, ``curl``, ``tar``.
  See `# 114`_, `# 116`_.

.. _# 114: https://github.com/ekalinin/nodeenv/pull/114
.. _# 116: https://github.com/ekalinin/nodeenv/pull/116

Version 0.12.3
--------------
- Fixed check for installed curl/tar/etc for py3.

Version 0.12.2
--------------
- Added check for installed curl/egrep/sort/tar. See `# 110`_, `# 61`_

.. _# 110: https://github.com/ekalinin/nodeenv/issues/110
.. _# 61: https://github.com/ekalinin/nodeenv/issues/61

Version 0.12.1
--------------
- Removed dep for lxml in favor of stdlib HTMLParser. See `# 109`_
- Added integration with travis-ci. See `# 109`_
- Added some tests. See `# 109`_

.. _# 109: https://github.com/ekalinin/nodeenv/pull/109

Version 0.12.0
--------------
- Added support for io.js (new option ``--iojs``)
- Fixed ``get_last_stable_node_version`` for python3

Version 0.11.1
--------------
- Disallow prefix overridden by .npmrc. See `# 98`_, `# 100`_, `# 103`_

.. _# 98: https://github.com/ekalinin/nodeenv/issue/98
.. _# 100: https://github.com/ekalinin/nodeenv/pull/100
.. _# 103: https://github.com/ekalinin/nodeenv/pull/103

Version 0.11.0
--------------
- Improved dpkg rules. See `# 90`_
- Added --config-file option. See `# 91`_
- Read "./setup.cfg" in addition to "~/.nodeenvrc" by default. See `# 91`_
- Python3 compatibility. See `# 92`_, `# 93`_

.. _# 90: https://github.com/ekalinin/nodeenv/pull/90
.. _# 91: https://github.com/ekalinin/nodeenv/pull/91
.. _# 92: https://github.com/ekalinin/nodeenv/pull/92
.. _# 93: https://github.com/ekalinin/nodeenv/pull/93

Version 0.10.0
--------------
- Added support for config file. See `# 85`_
- Using of virtualenv detected via python not env variable. See `# 87`_
- Fixed ``freeze`` for zsh. See `# 88`_
- Added ``nodejs`` symlink creation. See `# 84`_

.. _# 85: https://github.com/ekalinin/nodeenv/pull/85
.. _# 87: https://github.com/ekalinin/nodeenv/pull/87
.. _# 88: https://github.com/ekalinin/nodeenv/pull/88

Version 0.9.6
-------------
- Removed ``os.symlink`` added in `# 76`_. See `# 84`_

.. _# 84: https://github.com/ekalinin/nodeenv/issues/84

Version 0.9.5
-------------
- Fixed a few spelling typos in README. See `# 74`_
- Fixed example of using --update option in README. See `# 74`_
- Improved args passing into shim script. See `# 75`_
- Try to find ``nodejs`` if used system-wide node as well. See `# 76`_
- Added ``assert`` if used system-wide node and it wasnt found. See `# 76`_
- Added ``-l`` option into ``freeze`` command. See `# 71`_

.. _# 71: https://github.com/ekalinin/nodeenv/issues/71
.. _# 74: https://github.com/ekalinin/nodeenv/issues/74
.. _# 75: https://github.com/ekalinin/nodeenv/pull/75
.. _# 76: https://github.com/ekalinin/nodeenv/pull/76

Version 0.9.4
-------------
- Fixed support for python2.6. See `# 70`_

.. _# 70: https://github.com/ekalinin/nodeenv/issues/70

Version 0.9.3
-------------
- Fixed npm when using prebuilt binaries on Mac OS X. See `# 68`_
- Fixed using ``system`` node under python3.4. See `# 43`_
- If used ``system`` node script ``bin/node`` always overwritten

.. _# 68: https://github.com/ekalinin/nodeenv/issues/68
.. _# 43: https://github.com/ekalinin/nodeenv/issues/43

Version 0.9.2
-------------
- Fixed infinite loop when system-wide node used. See `# 67`_

.. _# 67: https://github.com/ekalinin/nodeenv/issues/67

Version 0.9.1
-------------
- Fixed 'shim' script if used system-wide node
- Fixed shebang in the 'shim'
- Added shim with name 'node' in case of using system-wide node

Version 0.9.0
-------------
- Added `shim` script. See `# 59`_

.. _# 59: https://github.com/ekalinin/nodeenv/issues/59

Version 0.8.2
-------------
- Fixed prebuilt installation:
    - cp more choosy. See `# 64`_
    - exclude top-level files in prebuilt tar. See `# 63`_

.. _# 63: https://github.com/ekalinin/nodeenv/issues/63
.. _# 64: https://github.com/ekalinin/nodeenv/issues/64

Version 0.8.1
-------------
- Fixed system's node usage. See `# 62`_

.. _# 62: https://github.com/ekalinin/nodeenv/pull/62

Version 0.8.0
-------------
- Added support for prebuilt packages. See `# 56`_
- Added support for python3. See `# 42`_

.. _# 56: https://github.com/ekalinin/nodeenv/issues/56
.. _# 42: https://github.com/ekalinin/nodeenv/issues/42

Version 0.7.3
-------------
- Fixed npm.js redirect. See `# 52`_
- Added ``--update`` option. See `# 25`_

.. _# 52: https://github.com/ekalinin/nodeenv/pull/52
.. _# 25: https://github.com/ekalinin/nodeenv/issues/25

Version 0.7.2
-------------
- Bug fixing in ``freeze``. See `# 47`_

.. _# 47: https://github.com/ekalinin/nodeenv/issues/47

Version 0.7.1
-------------
- Added ``--make`` option

Version 0.7.0
-------------
- added ``--force``. See `# 37`_
- python3 support (first steps). See `# 38`_, `# 39`_
- escaping all directories used by subprocesses. See `# 40`_
- added an option to use system-wide node. See `# 41`_

.. _# 37: https://github.com/ekalinin/nodeenv/pull/37
.. _# 38: https://github.com/ekalinin/nodeenv/issues/38
.. _# 39: https://github.com/ekalinin/nodeenv/issues/39
.. _# 40: https://github.com/ekalinin/nodeenv/pull/40
.. _# 41: https://github.com/ekalinin/nodeenv/pull/41


Version 0.6.6
-------------
- Fixed incomplete package names using freeze. See `# 35`_
- Fixed packages installation type in README. See `# 33`_

.. _# 33: https://github.com/ekalinin/nodeenv/issues/33
.. _# 35: https://github.com/ekalinin/nodeenv/pull/35


Version 0.6.5
-------------
- Node's source not loaded if it already exists in FS.


Version 0.6.4
-------------
- Added python3 compatibility. See `# 32`_

.. _# 32: https://github.com/ekalinin/nodeenv/pull/32


Version 0.6.3
-------------
- Fixed ``nodeenv -p``. See `issue # 31`_

.. _issue # 31: https://github.com/ekalinin/nodeenv/issues/31


Version 0.6.2
-------------
- Skipped stand-alone npm installation by default. See `pull # 30`_
- ``--without-npm`` has been changed to ``--with-npm``. See `pull # 30`_
- Fixed options for running Make instances in parallel. See `pull # 30`_

.. _pull # 30: https://github.com/ekalinin/nodeenv/pull/30


Version 0.6.1
-------------
- Used pkg_resources.parse_version to compare versions. See `pull # 29`_
- Fixed doubling prompt inside a virtualenv. See `issues # 26`_

.. _pull # 29: https://github.com/ekalinin/nodeenv/pull/29
.. _issues # 26: https://github.com/ekalinin/nodeenv/issues/26


Version 0.6.0
-------------
- Fixed typo in deactive command. See `pull # 20`_
- Not used "--insecure" to download npm. See `pull # 21`_
- Removed "src" directory after installation. See `pull # 22`_
- Added option "--clean-src"
- Added integration with python's virtualenv. See `pull # 23`_

.. _pull # 20: https://github.com/ekalinin/nodeenv/pull/20
.. _pull # 21: https://github.com/ekalinin/nodeenv/pull/21
.. _pull # 22: https://github.com/ekalinin/nodeenv/pull/22
.. _pull # 23: https://github.com/ekalinin/nodeenv/pull/23

Version 0.5.3
-------------
- Bug fix. Used https, /dist/latest/. See `pull # 16`_

.. _pull # 16: https://github.com/ekalinin/nodeenv/pull/16

Version 0.5.2
-------------
- Improved installation logic for release candidate versions. See `pull # 10`_

.. _pull # 10: https://github.com/ekalinin/nodeenv/pull/10

Version 0.5.1
-------------
- Improved logic for the option '--without-npm'. See `issue # 14`_, `pull # 15`_

.. _issue # 14: https://github.com/ekalinin/nodeenv/issues/14
.. _pull # 15: https://github.com/ekalinin/nodeenv/pull/15

Version 0.5.0
-------------
- The virtual environment's path is no longer hardcoded into the
  activation script. See `pull # 13`_

.. _pull # 13: https://github.com/ekalinin/nodeenv/pull/13

Version 0.4.3
-------------
- Fixed metavar for ``--npm``
- ``npm install -g`` used for npm >=1.0, not noly for `latest`

Version 0.4.2
-------------
- Added README.ru.rst

Version 0.4.1
-------------
- Fixed bug in print_node_versions. See `pull # 11`_
- Added deps in README

.. _pull # 11: https://github.com/ekalinin/nodeenv/pull/11

Version 0.4.0
-------------
- Compatibility with virtualenv (renamed deactivate to deactivate_node).
  See `pull # 9`_, `issue # 6`_

.. _pull # 9: https://github.com/ekalinin/nodeenv/pull/9
.. _issue # 6: https://github.com/ekalinin/nodeenv/issues/6

Version 0.3.10
--------------
- Fixed bug in url detection for node.js download

Version 0.3.9
-------------
- Deleted notify method calls. See `pull # 7`_
- Updated tar url for node.js > 0.5.0. See `pull # 8`_

.. _pull # 7: https://github.com/ekalinin/nodeenv/pull/7
.. _pull # 8: https://github.com/ekalinin/nodeenv/pull/8

Version 0.3.8
-------------
- Added `NODE_PATH` variable export (for correct module
  search after installation via npm)

Version 0.3.7
-------------
- Shows command output when error occurs
- Excluded 'npm' from `freeze` list
- Fixed bug with 'not only letter' names in `freeze` list
- Added global installation for npm >= 1.0 (when install
  soft from requirement file)

Version 0.3.6
-------------
- Fixed ``freeze`` output command. See `request # 5`_
- Diagnostic message fixed. See `pull # 4`_

.. _request # 5 : https://github.com/ekalinin/nodeenv/issues/5
.. _pull # 4 : https://github.com/ekalinin/nodeenv/pull/4

Version 0.3.5
-------------
- Added option ``--npm`` to install certain npm.js version. Request_ .
- Fixed ``freeze`` command for npm >= 1.0.x.

.. _Request: https://github.com/ekalinin/nodeenv/issues/3

Version 0.3.4
-------------

- Fixed `problem #2`_ with new npm installation script. Added
  ``--no-npm-clean`` option. The default to the npm 0.x cleanup.

.. _problem #2: https://github.com/ekalinin/nodeenv/issues/2

Version 0.3.3
-------------

- Fixed `problem #1`_ with installation from PyPI via easy_install.
  Added ``MANIFEST.in`` file.

.. _problem #1: https://github.com/ekalinin/nodeenv/issues/1

Version 0.3.2
-------------

- Internal improvements
- Logging refactoring

Version 0.3.1
-------------

- Default environment promt is folder name

Version 0.3.0
--------------

- Renamed nve to nodeenv
