from __future__ import annotations

import contextlib
import os.path
from collections.abc import Generator
from collections.abc import Sequence

from pre_commit import lang_base
from pre_commit.envcontext import envcontext
from pre_commit.envcontext import PatchesT
from pre_commit.envcontext import Var
from pre_commit.errors import FatalError
from pre_commit.prefix import Prefix

ENVIRONMENT_DIR = 'hs_env'
get_default_version = lang_base.basic_get_default_version
health_check = lang_base.basic_health_check
run_hook = lang_base.basic_run_hook


def get_env_patch(target_dir: str) -> PatchesT:
    bin_path = os.path.join(target_dir, 'bin')
    return (('PATH', (bin_path, os.pathsep, Var('PATH'))),)


@contextlib.contextmanager
def in_env(prefix: Prefix, version: str) -> Generator[None]:
    envdir = lang_base.environment_dir(prefix, ENVIRONMENT_DIR, version)
    with envcontext(get_env_patch(envdir)):
        yield


def install_environment(
    prefix: Prefix,
    version: str,
    additional_dependencies: Sequence[str],
) -> None:
    lang_base.assert_version_default('haskell', version)
    envdir = lang_base.environment_dir(prefix, ENVIRONMENT_DIR, version)

    pkgs = [*prefix.star('.cabal'), *additional_dependencies]
    if not pkgs:
        raise FatalError('Expected .cabal files or additional_dependencies')

    bindir = os.path.join(envdir, 'bin')
    os.makedirs(bindir, exist_ok=True)
    lang_base.setup_cmd(prefix, ('cabal', 'update'))
    lang_base.setup_cmd(
        prefix,
        (
            'cabal', 'install',
            '--install-method', 'copy',
            '--installdir', bindir,
            *pkgs,
        ),
    )
