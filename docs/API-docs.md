API docs:

https://ai-nav.onrender.com/api-docs (browser)

https://ai-nav.onrender.com/api-docs-json (endpoint)

POST /entities

{
  "name": "Awesome AI Tool",
  "website_url": "https://awesomeaitool.com",
  "entity_type_id": "valid-uuid-for-entity-type",
  "short_description": "A tool that revolutionizes AI development.",
  "description": "string",
  "logo_url": "https://awesomeaitool.com/logo.png",
  "documentation_url": "string",
  "contact_url": "string",
  "privacy_policy_url": "string",
  "founded_year": 2021,
  "social_links": {
    "twitter": "handle",
    "linkedin": "company/profile"
  },
  "category_ids": [
    "uuid-cat1"
  ],
  "tag_ids": [
    "uuid-tag1"
  ],
  "feature_ids": [
    "uuid-feat1",
    "uuid-feat2"
  ],
  "meta_title": "Awesome AI Tool | Best AI Solutions",
  "meta_description": "Discover the Awesome AI Tool, a leader in AI development solutions.",
  "employee_count_range": "C11_50",
  "funding_stage": "SERIES_A",
  "location_summary": "San Francisco, Remote",
  "ref_link": "https://awesomeaitool.com/?ref=partner",
  "affiliate_status": "APPROVED",
  "scraped_review_sentiment_label": "Positive",
  "scraped_review_sentiment_score": 0.85,
  "scraped_review_count": 150,
  "status": "PENDING",
  "tool_details": {
    "learning_curve": "string",
    "key_features": [
      "Real-time collaboration",
      "Advanced analytics"
    ],
    "has_free_tier": false,
    "use_cases": [
      "Data Analysis",
      "Machine Learning"
    ],
    "pricing_model": "string",
    "price_range": "FREE",
    "pricing_details": "string",
    "pricing_url": "string",
    "integrations": [
      "Slack",
      "Google Drive"
    ],
    "support_email": "string",
    "has_live_chat": false,
    "community_url": "string",
    "programming_languages": [
      "string"
    ],
    "frameworks": [
      "string"
    ],
    "libraries": [
      "string"
    ],
    "target_audience": [
      "string"
    ],
    "deployment_options": [
      "string"
    ],
    "supported_os": [
      "string"
    ],
    "mobile_support": true,
    "api_access": true,
    "customization_level": "string",
    "trial_available": true,
    "demo_available": true,
    "open_source": true,
    "support_channels": [
      "string"
    ],
    "technical_level": "INTERMEDIATE",
    "platforms": [
      "Web",
      "macOS",
      "iOS",
      "Android"
    ],
    "has_api": false,
    "api_documentation_url": "https://awesomeaitool.com/docs/api"
  },
  "course_details": {
    "instructor_name": "string",
    "duration_text": "string",
    "skill_level": "BEGINNER",
    "prerequisites": "string",
    "syllabus_url": "string",
    "enrollment_count": 0,
    "certificate_available": true
  },
  "agency_details": {
    "services_offered": [
      "AI Strategy",
      "ML Model Development"
    ],
    "industry_focus": [
      "Healthcare",
      "Finance"
    ],
    "target_client_size": [
      "Startup",
      "Enterprise"
    ],
    "target_audience": [
      "CTOs",
      "Product Managers"
    ],
    "location_summary": "New York, Remote",
    "portfolio_url": "https://agency.com/portfolio",
    "pricing_info": "Project-based, Retainer options available"
  },
  "content_creator_details": {
    "creator_name": "John Doe",
    "primary_platform": "YouTube",
    "focus_areas": [
      "AI Tutorials",
      "Tech Reviews"
    ],
    "follower_count": 100000,
    "example_content_url": "https://youtube.com/watch?v=example"
  },
  "community_details": {
    "platform": "Discord",
    "member_count": 1500,
    "focus_topics": [
      "AI Safety",
      "Large Language Models"
    ],
    "rules_url": "https://community.com/rules",
    "invite_url": "https://discord.gg/invitecode",
    "main_channel_url": "https://community.com/general"
  },
  "newsletter_details": {
    "frequency": "Weekly",
    "main_topics": [
      "AI Research",
      "Tech Industry News"
    ],
    "archive_url": "https://newsletter.com/archive",
    "subscribe_url": "https://newsletter.com/subscribe",
    "author_name": "AI Insights Team",
    "subscriber_count": 5000
  },
  "dataset_details": {
    "format": "CSV",
    "source_url": "https://example.com/dataset.zip",
    "license": "CC BY 4.0",
    "size_in_bytes": 104857600,
    "description": "A comprehensive dataset of...",
    "access_notes": "Requires registration. Download link will be emailed."
  },
  "research_paper_details": {
    "publication_date": "2023-03-15",
    "doi": "10.1000/xyz123",
    "authors": [
      "Geoffrey Hinton",
      "Yann LeCun",
      "Yoshua Bengio"
    ],
    "research_areas": [
      "Machine Learning",
      "Natural Language Processing",
      "Computer Vision"
    ],
    "publication_venues": [
      "NeurIPS",
      "ICML",
      "Nature"
    ],
    "keywords": [
      "transformer",
      "attention mechanism",
      "neural network",
      "deep learning"
    ],
    "arxiv_id": "2301.12345",
    "abstract": "This paper explores novel approaches to transformer architectures...",
    "journal_or_conference": "Journal of AI Research",
    "pdf_url": "https://arxiv.org/pdf/2301.12345.pdf",
    "citation_count": 150
  },
  "software_details": {
    "repository_url": "https://github.com/company/awesome-ai-tool",
    "license_type": "MIT",
    "programming_languages": [
      "Python",
      "JavaScript",
      "TypeScript"
    ],
    "platform_compatibility": [
      "Windows",
      "macOS",
      "Linux",
      "Web"
    ],
    "current_version": "2.1.0",
    "release_date": "2024-01-15",
    "open_source": true,
    "has_free_tier": true,
    "use_cases": [
      "string"
    ],
    "pricing_model": "FREE",
    "price_range": "FREE",
    "pricing_details": "string",
    "pricing_url": "string",
    "integrations": [
      "string"
    ],
    "support_email": "string",
    "has_live_chat": true,
    "community_url": "string",
    "api_access": true,
    "customization_level": "string",
    "demo_available": true,
    "deployment_options": [
      "string"
    ],
    "frameworks": [
      "string"
    ],
    "has_api": true,
    "key_features": [
      "string"
    ],
    "libraries": [
      "string"
    ],
    "mobile_support": true,
    "support_channels": [
      "string"
    ],
    "supported_os": [
      "string"
    ],
    "target_audience": [
      "string"
    ],
    "trial_available": true
  },
  "model_details": {
    "model_architecture": "Transformer",
    "parameters_count": 175000000000,
    "training_dataset": "Trained on a large corpus of text and code.",
    "performance_metrics": {
      "accuracy": 0.95,
      "f1_score": 0.92
    },
    "model_url": "https://huggingface.co/openai-gpt",
    "license": "MIT"
  },
  "project_reference_details": {
    "project_status": "active",
    "source_code_url": "https://github.com/user/project",
    "live_demo_url": "https://project-demo.com",
    "technologies": [
      "React",
      "Node.js",
      "PostgreSQL"
    ],
    "project_goals": "To demonstrate advanced AI capabilities in X domain.",
    "contributors": [
      "Jane Doe",
      {
        "name": "John Smith",
        "role": "Lead Developer"
      }
    ]
  },
  "service_provider_details": {
    "service_areas": [
      "AI Development",
      "Data Science Consulting",
      "MLOps"
    ],
    "case_studies_url": "https://serviceprovider.com/case-studies",
    "consultation_booking_url": "https://serviceprovider.com/book-consultation",
    "industry_specializations": [
      "Healthcare",
      "Finance"
    ],
    "company_size_focus": "SMEs",
    "hourly_rate_range": "$100-$200"
  },
  "investor_details": {
    "investment_focus_areas": [
      "Seed Stage AI",
      "Healthcare Tech",
      "SaaS"
    ],
    "portfolio_url": "https://investor.com/portfolio",
    "typical_investment_size": "$100k - $1M",
    "investment_stages": [
      "Pre-seed",
      "Seed",
      "Series A"
    ],
    "contact_email": "<EMAIL>",
    "preferred_communication": "Email, LinkedIn"
  },
  "event_details": {
    "event_type": "Conference",
    "start_date": "2024-09-15T09:00:00Z",
    "end_date": "2024-09-17T17:00:00Z",
    "location": "San Francisco, CA",
    "is_online": true,
    "event_format": "hybrid",
    "registration_required": true,
    "registration_url": "https://eventbrite.com/event/123",
    "capacity": 500,
    "organizer": "AI Conference Organization",
    "key_speakers": [
      "Dr. AI Expert",
      "Jane Innovations",
      "Prof. Machine Learning"
    ],
    "target_audience": [
      "Developers",
      "Data Scientists",
      "AI Researchers",
      "Business Leaders"
    ],
    "topics": [
      "Machine Learning",
      "Natural Language Processing",
      "Computer Vision"
    ],
    "price": "$99"
  },
  "job_details": {
    "company_name": "Tech Solutions Inc.",
    "employment_types": [
      "FULL_TIME",
      "CONTRACT"
    ],
    "experience_level": "MID",
    "location_types": [
      "Remote",
      "Hybrid"
    ],
    "salary_min": 80,
    "salary_max": 120,
    "application_url": "https://jobs.example.com/apply/123",
    "job_description": "Seeking an experienced AI engineer to develop cutting-edge solutions...",
    "is_remote": true,
    "location": "San Francisco, CA",
    "job_type": "Software Engineering",
    "key_responsibilities": [
      "Develop AI models",
      "Collaborate with data scientists",
      "Deploy ML solutions"
    ],
    "required_skills": [
      "Python",
      "TensorFlow",
      "Machine Learning",
      "Docker"
    ],
    "benefits": [
      "Health Insurance",
      "Stock Options",
      "Remote Work",
      "Flexible Hours"
    ],
    "remote_policy": "Fully Remote",
    "visa_sponsorship": false
  },
  "grant_details": {
    "granting_institution": "AI Research Foundation",
    "eligibility_criteria": "Must be a non-profit organization focused on AI safety.",
    "application_deadline": "2024-12-31",
    "funding_amount": "$10,000 - $50,000",
    "application_url": "https://foundation.example/apply-grant",
    "grant_focus_area": "AI Ethics Research"
  },
  "bounty_details": {
    "bounty_issuer": "OpenAI",
    "reward_amount": "1000 USD",
    "requirements": "Develop a plugin for X, must meet performance Y.",
    "submission_deadline": "2024-11-30",
    "platform_url": "https://gitcoin.co/bounty/123",
    "difficulty_level": "Medium"
  },
  "hardware_details": {
    "hardware_type": "GPU",
    "manufacturer": "NVIDIA",
    "release_date": "2023-09-20",
    "specifications": {
      "memory": "24GB GDDR6X",
      "cuda_cores": 10496,
      "tflops": 35.6,
      "architecture": "Ada Lovelace"
    },
    "datasheet_url": "https://nvidia.com/datasheet/rtx4070.pdf",
    "memory": "24GB GDDR6X",
    "processor": "Ada Lovelace GPU",
    "storage": "1TB NVMe SSD",
    "power_consumption": "220W TGP",
    "availability": "In Stock",
    "price": "$799 MSRP",
    "gpu": "RTX 4070 Ti",
    "use_cases": [
      "Gaming",
      "AI Training",
      "Content Creation"
    ]
  },
  "news_details": {
    "publication_date": "2024-03-20",
    "source_name": "TechCrunch",
    "articleUrl": "https://techcrunch.com/ai-breakthrough",
    "author": "Jane Doe",
    "summary": "A new AI model has achieved state-of-the-art results in...",
    "status": "ACTIVE"
  },
  "book_details": {
    "author_names": [
      "Author A",
      "Author B"
    ],
    "isbn": "978-3-16-148410-0",
    "publisher": "Tech Publishing House",
    "publication_year": 2023,
    "page_count": 350,
    "summary": "An in-depth look at the future of artificial intelligence...",
    "purchase_url": "https://amazon.com/book-title"
  },
  "podcast_details": {
    "host_names": [
      "Host One",
      "Host Two"
    ],
    "average_episode_length": "45 minutes",
    "main_topics": [
      "AI Ethics",
      "ML Research",
      "Tech News"
    ],
    "listen_url": "https://open.spotify.com/show/podcastid",
    "frequency": "Weekly",
    "primary_language": "English"
  },
  "platform_details": {
    "platform_type": "SaaS",
    "key_services": [
      "Model Training",
      "Data Annotation"
    ],
    "documentation_url": "https://platform.com/docs",
    "pricing_model": "SUBSCRIPTION",
    "sla_url": "https://platform.com/sla",
    "supported_regions": [
      "us-east-1",
      "eu-west-2"
    ],
    "has_free_tier": true,
    "use_cases": [
      "Enterprise AI Solutions",
      "Startup AI Development"
    ],
    "price_range": "MEDIUM",
    "pricing_details": "Basic: $99/month, Pro: $299/month",
    "pricing_url": "https://platform.com/pricing",
    "integrations": [
      "Slack",
      "GitHub"
    ],
    "support_email": "<EMAIL>",
    "has_live_chat": false,
    "community_url": "https://community.platform.com"
  }
}
