
Implementation Plan: AI Resource Enhancement Pipeline (AREP)

This document outlines the step-by-step plan for implementing the AI Resource Enhancement Pipeline (AREP). Each step is designed to be a small, verifiable unit of work.

Developer Onboarding:

Log your progress: After completing each step, add a comment in a personal log or a shared document with the step number, date, and any notes. This creates a detailed history of the project's development.

Commit frequently: After each step (or a small group of steps), commit your changes to Git with a clear message. This makes it easy to revert to a stable state if something goes wrong.

Ask questions: If any instruction is unclear, do not guess. Please ask for clarification.

Part 1: Project Foundation & Setup (Steps 1-10)

Goal: Establish a robust project structure, environment, and basic configurations.

Step 1: Initialize Git Repository

Instruction: Create a new directory for the project and initialize a Git repository. This will be the root of our project for version control.

Commands:

Generated bash
mkdir ai-resource-enhancer
cd ai-resource-enhancer
git init


Step 2: Create Project Directory Structure

Instruction: Create the core directories and empty Python package files. This organizes the code logically by function.

Commands:

Generated bash
mkdir arep
touch arep/__init__.py
mkdir arep/api arep/collectors arep/classification arep/enhancement arep/monitoring arep/utils
touch arep/api/__init__.py
touch arep/collectors/__init__.py
touch arep/classification/__init__.py
touch arep/enhancement/__init__.py
touch arep/monitoring/__init__.py
touch arep/utils/__init__.py
mkdir tests
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

Step 3: Set Up Python Virtual Environment

Instruction: Create and activate a Python virtual environment to isolate project dependencies.

Documentation: Python venv Docs

Commands:

Generated bash
python3 -m venv venv
source venv/bin/activate
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

Step 4: Install Core Dependencies

Instruction: Install the essential Python libraries for the project.

Commands:

Generated bash
pip install "fastapi[all]" uvicorn python-dotenv redis aiohttp beautifulsoup4 lxml tenacity
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

Step 5: Create requirements.txt

Instruction: Freeze the current list of dependencies into a requirements.txt file.

Command:

Generated bash
pip freeze > requirements.txt
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

Step 6: Configure Environment Variables

Instruction: Create a .env.example file to template the required secrets and a .env file for local development. Add .env to .gitignore.

.env.example content:

Generated code
AI_NAV_AUTH_TOKEN="YOUR_API_KEY_HERE"
PERPLEXITY_API_KEY="YOUR_API_KEY_HERE"
BRAVE_API_KEY="YOUR_API_KEY_HERE"
OPENAI_API_KEY="YOUR_API_KEY_HERE"
REDIS_URL="redis://localhost:6379"
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

Commands:

Generated bash
cp .env.example .env
echo ".env" >> .gitignore
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

(Now, edit the .env file with your actual credentials)

Step 7: Create Configuration Loader

Instruction: Create a file arep/config.py to load and manage all configurations and secrets from environment variables.

Documentation: python-dotenv

arep/config.py content:

Generated python
import os
from dotenv import load_dotenv

load_dotenv()

# API Configurations
AI_NAV_API_URL = "https://ai-nav.onrender.com"
AI_NAV_AUTH_TOKEN = os.getenv("AI_NAV_AUTH_TOKEN")

# Search Provider Keys
PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY")
BRAVE_API_KEY = os.getenv("BRAVE_API_KEY")

# LLM Provider Keys
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Infrastructure
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 8: Set Up Centralized Logging

Instruction: Create arep/utils/logger.py to configure a standardized logger for the entire application.

Documentation: Python Logging HOWTO

arep/utils/logger.py content:

Generated python
import logging
import sys

def get_logger(name: str):
    logger = logging.getLogger(name)
    if not logger.handlers:
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    return logger

log = get_logger(__name__)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 9: Create Basic Health Check Endpoint

Instruction: Create a main.py at the project root with a simple FastAPI app and a /health endpoint to confirm the server runs.

main.py content:

Generated python
from fastapi import FastAPI

app = FastAPI(title="AI Resource Enhancement Pipeline")

@app.get("/health", status_code=200, tags=["Monitoring"])
def health_check():
    return {"status": "ok"}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Run it:

Generated bash
uvicorn main:app --reload
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

(Visit http://127.0.0.1:8000/health in your browser)

Step 10: Set Up Pre-Commit Hooks

Instruction: Use pre-commit to automatically format and lint code, ensuring consistency.

Documentation: pre-commit

Commands:

Generated bash
pip install pre-commit
pre-commit install
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

.pre-commit-config.yaml content:

Generated yaml
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
-   repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
    -   id: black
-   repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
    -   id: isort
        name: isort (python)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Yaml
IGNORE_WHEN_COPYING_END
Part 2: Core Data Models & API Submitter (Steps 11-20)

Goal: Define all data structures and build the client to communicate with the AI Navigator API.

Step 11: Create Base Pydantic Models

Instruction: In a new file arep/models.py, define the basic Pydantic models that represent data as it flows through the initial stages of the pipeline.

Documentation: Pydantic Models

arep/models.py content:

Generated python
from pydantic import BaseModel, HttpUrl
from typing import Optional, List
from datetime import datetime

class MinimalEntity(BaseModel):
    name: str
    url: HttpUrl
    logo_url: Optional[HttpUrl] = None
    source: str
    discovered_at: datetime

class ClassificationResult(BaseModel):
    entity_type: str
    entity_type_id: str
    confidence: float

class ClassifiedEntity(MinimalEntity, ClassificationResult):
    pass
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 12: Define API Schema Models

Instruction: Based on the example POST call in the PRD, create comprehensive Pydantic models for all known entity types in arep/api/schemas.py. This is a large but critical step.

arep/api/schemas.py content (Tool Details example):
Create a file and add Pydantic classes for every *_details object and the main EntityPayload.

Generated python
from pydantic import BaseModel, HttpUrl
from typing import List, Optional, Dict, Any

class ToolDetails(BaseModel):
    learning_curve: Optional[str] = None
    key_features: Optional[List[str]] = []
    has_free_tier: Optional[bool] = None
    # ... add ALL other fields from the PRD for tool_details
    has_api: Optional[bool] = None

# ... Define CourseDetails, NewsletterDetails, etc. for all types

class EntityPayload(BaseModel):
    name: str
    website_url: HttpUrl
    entity_type_id: str
    short_description: Optional[str] = None
    logo_url: Optional[HttpUrl] = None
    status: str = "PENDING"
    tool_details: Optional[ToolDetails] = None
    course_details: Optional[dict] = None # Replace with a real model
    # ... Add all other optional detail objects
    # ... Add all other top-level fields like category_ids, etc.
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

(Note: This is a large task. Be meticulous and map every field from the PRD's example POST call.)

Step 13: Create the API Submitter Class

Instruction: Create the file arep/api/submitter.py and define the EntitySubmitter class structure.

arep/api/submitter.py content:

Generated python
import aiohttp
from arep.config import AI_NAV_API_URL, AI_NAV_AUTH_TOKEN
from arep.utils.logger import log
from .schemas import EntityPayload

class EntitySubmitter:
    def __init__(self):
        self.api_url = AI_NAV_API_URL
        self.auth_token = AI_NAV_AUTH_TOKEN
        self.headers = {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
        self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(headers=self.headers)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.close()

    # Methods will be added in next steps
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 14: Implement the submit_entity Method

Instruction: Add the submit_entity method to the EntitySubmitter class to handle POST requests.

Documentation: aiohttp Client Quickstart

Add to EntitySubmitter class:

Generated python
async def submit_entity(self, entity_payload: EntityPayload) -> dict:
    post_url = f"{self.api_url}/entities"
    payload = entity_payload.model_dump(exclude_none=True)
    try:
        async with self.session.post(post_url, json=payload) as response:
            log.info(f"API Response for {entity_payload.name}: {response.status}")
            if response.status == 201:
                return {"success": True, "data": await response.json()}
            # Handle other statuses like 409 (Conflict/Duplicate) later
            else:
                error_text = await response.text()
                log.error(f"API Error: {response.status} - {error_text}")
                return {"success": False, "status": response.status, "error": error_text}
    except Exception as e:
        log.exception(f"Exception during entity submission for {entity_payload.name}")
        return {"success": False, "error": str(e)}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 15: Implement tenacity for Retries

Instruction: Add retry logic to the submission method to handle transient network issues.

Documentation: Tenacity Library

Update arep/api/submitter.py:

Generated python
# Add to imports
from tenacity import retry, stop_after_attempt, wait_exponential

# ... inside EntitySubmitter class ...
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def submit_entity(self, entity_payload: EntityPayload) -> dict:
    # ... (rest of the function is the same)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 16-20: Placeholder for further API interaction (Will be detailed if needed)

These steps would include handling duplicates (handle_duplicate), fetching existing entities, and updating them. For now, the core submit_entity is sufficient to proceed with the pipeline.

Part 3: Data Collection & Classification (Steps 21-30)

Goal: Implement the logic for collecting minimal entity data and classifying its type.

Step 21: Create Base Scraper

Instruction: In arep/collectors/base.py, create a base class for all scrapers to inherit from, ensuring a consistent interface.

arep/collectors/base.py content:

Generated python
from abc import ABC, abstractmethod
from typing import List
from arep.models import MinimalEntity

class BaseScraper(ABC):
    @abstractmethod
    async def scrape(self) -> List[dict]:
        """Returns a list of dicts with at least name, url."""
        pass
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 22: Implement Favicon URL Extractor

Instruction: Create a utility function in arep/utils/favicon.py to generate a Google favicon URL as a fallback for logos.

arep/utils/favicon.py content:

Generated python
from urllib.parse import urlparse

def get_favicon_url(url: str) -> str:
    """Fallback to extract favicon if no logo provided."""
    parsed_url = urlparse(url)
    domain = parsed_url.netloc or parsed_url.path
    return f"https://www.google.com/s2/favicons?domain={domain}&sz=128"
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 23: Implement Minimal Data Collector

Instruction: Create arep/collectors/collector.py to orchestrate different scrapers (which are currently placeholders).

arep/collectors/collector.py content:

Generated python
from datetime import datetime
from arep.models import MinimalEntity
from arep.utils.favicon import get_favicon_url
from arep.utils.logger import log

class MinimalDataCollector:
    def __init__(self):
        # In the future, this will dynamically load scraper instances
        self.scrapers = []

    async def collect(self) -> list[MinimalEntity]:
        # For now, use a hardcoded list for testing
        mock_data = [
            {"name": "Perplexity AI", "url": "https://perplexity.ai"},
            {"name": "OpenAI", "url": "https://openai.com", "logo": "url_to_logo"},
        ]

        entities = []
        for item in mock_data:
            entities.append(MinimalEntity(
                name=item['name'],
                url=item['url'],
                logo_url=item.get('logo', get_favicon_url(item['url'])),
                source='mock_source',
                discovered_at=datetime.utcnow()
            ))
        log.info(f"Collected {len(entities)} minimal entities.")
        return entities
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 24: Implement Entity Classifier Structure

Instruction: Create arep/classification/classifier.py and set up the EntityTypeClassifier class.

arep/classification/classifier.py content:

Generated python
from arep.models import MinimalEntity, ClassificationResult

class EntityTypeClassifier:
    async def classify(self, entity: MinimalEntity) -> ClassificationResult:
        # This is a mock classification. It will be improved.
        # In a real scenario, this would involve fetching page data,
        # analyzing content, and maybe an LLM call.

        # Simple rule-based mock
        if "tool" in entity.name.lower() or "ai" in entity.name.lower():
            entity_type = "tool"
        elif "course" in entity.name.lower():
            entity_type = "course"
        else:
            entity_type = "tool" # Default

        # UUIDs would be fetched from the API in a real scenario
        mock_type_ids = {"tool": "d8a011a6-446b-4546-9573-41c55b409b55"}

        return ClassificationResult(
            entity_type=entity_type,
            entity_type_id=mock_type_ids.get(entity_type, "d8a011a6-446b-4546-9573-41c55b409b55"),
            confidence=0.5
        )
```**Step 25-30: Placeholder for more advanced classification and scraping.**
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

The current mock implementations are sufficient to build and test the rest of the pipeline structure. Advanced logic (LLM calls, real scraping) can be filled in later.

Part 4: Research, Enhancement & Pipeline Orchestration (Steps 31-45)

Goal: Build the engine that researches an entity and fills in the data, and orchestrate the full pipeline flow.

Step 31: Create Research Engine Structure

Instruction: In arep/enhancement/research.py, define the SmartResearchEngine class.

arep/enhancement/research.py content:

Generated python
from arep.models import ClassifiedEntity

class SmartResearchEngine:
    async def research(self, entity: ClassifiedEntity) -> dict:
        # Mock research data.
        # A real implementation would use search APIs (Perplexity, Brave)
        # and scrape the entity's website.
        print(f"Researching {entity.name}...")
        return {
            "summary": f"This is a summary for {entity.name}.",
            "features": ["Feature A", "Feature B", "Feature C"],
            "pricing": "Freemium",
            "has_api": True
        }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 32: Create Base Enhancer

Instruction: In arep/enhancement/base.py, create a base class for type-specific enhancers.

arep/enhancement/base.py content:

Generated python
from abc import ABC, abstractmethod
from arep.models import ClassifiedEntity
from arep.api.schemas import EntityPayload

class BaseEnhancer(ABC):
    @abstractmethod
    async def enhance(self, entity: ClassifiedEntity, research_data: dict) -> EntityPayload:
        pass
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 33: Create Tool Enhancer

Instruction: In arep/enhancement/tool_enhancer.py, implement the enhancer for the "tool" entity type. This class translates research data into the final API schema.

arep/enhancement/tool_enhancer.py content:

Generated python
from arep.enhancement.base import BaseEnhancer
from arep.models import ClassifiedEntity
from arep.api.schemas import EntityPayload, ToolDetails

class ToolEnhancer(BaseEnhancer):
    async def enhance(self, entity: ClassifiedEntity, research_data: dict) -> EntityPayload:
        tool_details = ToolDetails(
            key_features=research_data.get("features"),
            has_api=research_data.get("has_api"),
            has_free_tier=research_data.get("pricing") in ["Freemium", "Free"]
        )

        payload = EntityPayload(
            name=entity.name,
            website_url=entity.url,
            entity_type_id=entity.entity_type_id,
            short_description=research_data.get("summary"),
            logo_url=entity.logo_url,
            tool_details=tool_details
        )
        return payload
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 34: Create the Main Pipeline Orchestrator

Instruction: Create arep/pipeline.py to manage the end-to-end process for a single entity.

arep/pipeline.py content:

Generated python
from arep.collectors.collector import MinimalDataCollector
from arep.classification.classifier import EntityTypeClassifier
from arep.enhancement.research import SmartResearchEngine
from arep.enhancement.tool_enhancer import ToolEnhancer # Add other enhancers here
from arep.api.submitter import EntitySubmitter
from arep.utils.logger import log
from arep.models import ClassifiedEntity

class EnhancementPipeline:
    def __init__(self):
        self.collector = MinimalDataCollector()
        self.classifier = EntityTypeClassifier()
        self.researcher = SmartResearchEngine()
        self.enhancers = {"tool": ToolEnhancer()} # Map types to enhancers
        self.submitter = EntitySubmitter()

    async def process_entity(self, minimal_entity):
        log.info(f"Processing entity: {minimal_entity.name}")
        # 1. Classify
        classification = await self.classifier.classify(minimal_entity)
        classified_entity = ClassifiedEntity(**minimal_entity.model_dump(), **classification.model_dump())

        # 2. Research
        research_data = await self.researcher.research(classified_entity)

        # 3. Enhance
        enhancer = self.enhancers.get(classified_entity.entity_type)
        if not enhancer:
            log.error(f"No enhancer found for type: {classified_entity.entity_type}")
            return

        entity_payload = await enhancer.enhance(classified_entity, research_data)

        # 4. Submit
        async with self.submitter as submitter:
            result = await submitter.submit_entity(entity_payload)
            if result.get("success"):
                log.info(f"Successfully submitted {entity_payload.name}")
            else:
                log.error(f"Failed to submit {entity_payload.name}: {result.get('error')}")

    async def run(self):
        minimal_entities = await self.collector.collect()
        for entity in minimal_entities:
            await self.process_entity(entity)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Step 35: Create Pipeline Runner

Instruction: Create run.py in the root directory to execute the pipeline.

run.py content:

Generated python
import asyncio
from arep.pipeline import EnhancementPipeline

async def main():
    pipeline = EnhancementPipeline()
    await pipeline.run()

if __name__ == "__main__":
    asyncio.run(main())
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Run it:

Generated bash
python run.py
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

(You should see log output for the mock entities being processed.)

Step 36-45: Placeholder for advanced features.

These steps will involve adding a real processing queue (Redis), a monitoring dashboard, cost optimization logic, and more sophisticated error handling. The current structure is a complete, albeit simplified, end-to-end flow.

Part 5: End-to-End (E2E) Testing (Steps 46-55)

Goal: Provide developers with a clear plan for creating, running, and debugging tests that validate the entire pipeline.

Step 46: Install Testing Dependencies

Instruction: Install pytest and pytest-asyncio for running our test suite.

Command:

Generated bash
pip install pytest pytest-asyncio
pip freeze > requirements.txt
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

Step 47: Configure Pytest

Instruction: Create a pytest.ini file in the root directory to configure the test runner.

pytest.ini content:

Generated ini
[pytest]
asyncio_mode = auto
python_files = tests.py test_*.py *_test.py
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Ini
IGNORE_WHEN_COPYING_END

Step 48: Create E2E Test Structure

Instruction: Create a file tests/e2e_test.py. This test will run the full pipeline against mocked external services.

tests/e2e_test.py content:

Generated python
import pytest
from unittest.mock import patch, MagicMock
from arep.pipeline import EnhancementPipeline

@pytest.mark.asyncio
@patch('arep.pipeline.MinimalDataCollector')
@patch('arep.pipeline.EntitySubmitter')
async def test_full_pipeline_flow(self, MockSubmitter, MockCollector):
    # 1. Setup Mocks
    # Mock Collector to return one specific test entity
    mock_collector_instance = MockCollector.return_value
    mock_collector_instance.collect.return_value = [
        # ... returns a MinimalEntity for "Test AI Tool" ...
    ]

    # Mock Submitter to simulate a successful API call
    mock_submitter_instance = MockSubmitter.return_value
    # Use __aenter__ for async context manager
    mock_submitter_instance.__aenter__.return_value.submit_entity.return_value = {
        "success": True,
        "data": {"id": "new-uuid"}
    }

    # 2. Run Pipeline
    pipeline = EnhancementPipeline()
    await pipeline.run()

    # 3. Assertions
    # Check that collector was called
    mock_collector_instance.collect.assert_called_once()

    # Check that submitter was called
    submit_mock = mock_submitter_instance.__aenter__.return_value.submit_entity
    submit_mock.assert_called_once()

    # Check the data that was submitted
    submitted_payload = submit_mock.call_args[0][0]
    assert submitted_payload.name == "Test AI Tool"
    assert submitted_payload.tool_details.has_api is True

    print("E2E test passed successfully!")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

(Note: This requires filling in the mock data for the MinimalEntity)

Step 49: Running the E2E Test

Instruction: Execute the test suite using the pytest command.

Command:

Generated bash
pytest
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

Expected Outcome: The test should pass, and the "E2E test passed successfully!" message should be visible. This confirms all parts of the pipeline are connected correctly.

Step 50: Debugging a Failed E2E Test

Instruction: If the E2E test fails, follow these steps:

Examine Pytest Output: Look at the traceback to identify which assertion failed.

Add Log/Print Statements: Add print() statements in the pipeline code (e.g., arep/pipeline.py) to inspect the data at each step (classification, research_data, entity_payload).

Run with -s flag: Use pytest -s to see the print output.

Isolate the Failing Component: If the data looks wrong after the "enhance" step, for example, write a separate unit test just for the ToolEnhancer to debug it in isolation.

Verify Mock Behavior: Ensure your mocks are configured correctly. A common error is the mock not returning the expected data structure.

Step 51-55: Placeholder for CI/CD integration.

Once tests are reliably passing locally, the next steps involve creating a GitHub Actions workflow (.github/workflows/ci.yml) to automatically run these tests on every push, preventing faulty code from being merged.
