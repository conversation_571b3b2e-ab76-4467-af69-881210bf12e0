"""
AI Navigator API client for submitting and managing resources.
Provides a robust HTTP client with authentication, error handling, and retry logic.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import aiohttp
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

from arep.config import AI_NAV_API_URL, AI_NAV_AUTH_TOKEN
from arep.utils.logger import get_logger
from .models import (
    APIResponse,
    Resource,
    ResourceSubmissionResponse,
    BatchSubmissionResponse,
    RateLimitInfo,
    RetryConfig,
    APIError,
)

logger = get_logger(__name__)


class APIClientError(Exception):
    """Base exception for API client errors."""
    pass


class AuthenticationError(APIClientError):
    """Raised when authentication fails."""
    pass


class RateLimitError(APIClientError):
    """Raised when rate limit is exceeded."""
    pass


class ValidationError(APIClientError):
    """Raised when request validation fails."""
    pass


class AINavigatorClient:
    """
    Async HTTP client for the AI Navigator API.
    
    Provides methods for submitting resources with built-in:
    - Authentication handling
    - Rate limiting
    - Retry logic with exponential backoff
    - Error handling and logging
    - Request/response validation
    """
    
    def __init__(
        self,
        api_url: str = AI_NAV_API_URL,
        auth_token: str = AI_NAV_AUTH_TOKEN,
        timeout: int = 30,
        retry_config: Optional[RetryConfig] = None,
    ):
        """
        Initialize the AI Navigator API client.
        
        Args:
            api_url: Base URL for the AI Navigator API
            auth_token: Authentication token for API access
            timeout: Request timeout in seconds
            retry_config: Configuration for retry logic
        """
        self.api_url = api_url.rstrip('/')
        self.auth_token = auth_token
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.retry_config = retry_config or RetryConfig()
        
        # Rate limiting
        self._rate_limit_info: Optional[RateLimitInfo] = None
        self._last_request_time: float = 0
        self._request_count: int = 0
        
        # Session management
        self._session: Optional[aiohttp.ClientSession] = None
        
        if not self.auth_token:
            raise AuthenticationError("AI Navigator auth token is required")
        
        logger.info(f"Initialized AI Navigator client for {self.api_url}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def _ensure_session(self):
        """Ensure aiohttp session is created."""
        if self._session is None or self._session.closed:
            headers = {
                'Authorization': f'Bearer {self.auth_token}',
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Resource-Enhancement-Pipeline/1.0',
            }
            self._session = aiohttp.ClientSession(
                headers=headers,
                timeout=self.timeout,
            )
    
    async def close(self):
        """Close the HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
    
    def _get_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Get request headers with authentication."""
        headers = {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Resource-Enhancement-Pipeline/1.0',
        }
        if additional_headers:
            headers.update(additional_headers)
        return headers
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        current_time = time.time()
        
        # Simple rate limiting: ensure minimum time between requests
        min_interval = 60.0 / 60  # 60 requests per minute = 1 request per second
        time_since_last = current_time - self._last_request_time
        
        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            await asyncio.sleep(sleep_time)
        
        self._last_request_time = time.time()
        self._request_count += 1
    
    def _parse_rate_limit_headers(self, headers: Dict[str, str]) -> Optional[RateLimitInfo]:
        """Parse rate limit information from response headers."""
        try:
            if 'X-RateLimit-Remaining' in headers and 'X-RateLimit-Reset' in headers:
                from datetime import datetime
                reset_timestamp = int(headers['X-RateLimit-Reset'])
                reset_time = datetime.fromtimestamp(reset_timestamp)

                return RateLimitInfo(
                    requests_per_minute=int(headers.get('X-RateLimit-Limit', 60)),
                    requests_remaining=int(headers['X-RateLimit-Remaining']),
                    reset_time=reset_time,
                )
        except (ValueError, KeyError) as e:
            logger.warning(f"Failed to parse rate limit headers: {e}")
        return None
    
    async def _handle_response(self, response: aiohttp.ClientResponse) -> Dict[str, Any]:
        """
        Handle API response and extract data.
        
        Args:
            response: aiohttp response object
            
        Returns:
            Parsed response data
            
        Raises:
            APIClientError: For various API errors
        """
        # Update rate limit info
        self._rate_limit_info = self._parse_rate_limit_headers(dict(response.headers))
        
        # Get response text
        try:
            response_text = await response.text()
            response_data = await response.json() if response_text else {}
        except Exception as e:
            logger.error(f"Failed to parse response: {e}")
            raise APIClientError(f"Invalid response format: {e}")
        
        # Handle different status codes
        if response.status == 200 or response.status == 201:
            logger.debug(f"Successful API request: {response.status}")
            return response_data
        
        elif response.status == 401:
            logger.error("Authentication failed")
            raise AuthenticationError("Invalid or expired authentication token")
        
        elif response.status == 429:
            logger.warning("Rate limit exceeded")
            raise RateLimitError("API rate limit exceeded")
        
        elif response.status == 422:
            logger.error(f"Validation error: {response_data}")
            raise ValidationError(f"Request validation failed: {response_data}")
        
        elif 400 <= response.status < 500:
            error_msg = response_data.get('message', f'Client error: {response.status}')
            logger.error(f"Client error {response.status}: {error_msg}")
            raise APIClientError(f"Client error: {error_msg}")
        
        elif 500 <= response.status < 600:
            error_msg = response_data.get('message', f'Server error: {response.status}')
            logger.error(f"Server error {response.status}: {error_msg}")
            raise APIClientError(f"Server error: {error_msg}")
        
        else:
            logger.error(f"Unexpected status code: {response.status}")
            raise APIClientError(f"Unexpected response status: {response.status}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=60),
        retry=retry_if_exception_type((aiohttp.ClientError, APIClientError)),
        reraise=True,
    )
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Make an HTTP request with retry logic.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            data: Request body data
            params: Query parameters
            headers: Additional headers
            
        Returns:
            Response data
        """
        await self._ensure_session()
        await self._check_rate_limit()
        
        url = urljoin(self.api_url + '/', endpoint.lstrip('/'))
        request_headers = self._get_headers(headers)
        
        logger.debug(f"Making {method} request to {url}")
        
        try:
            async with self._session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers=request_headers,
            ) as response:
                return await self._handle_response(response)
        
        except aiohttp.ClientError as e:
            logger.error(f"HTTP client error: {e}")
            raise APIClientError(f"HTTP request failed: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error during request: {e}")
            raise APIClientError(f"Request failed: {e}")
    
    async def health_check(self) -> bool:
        """
        Check if the API is healthy and accessible.

        Returns:
            True if API is healthy, False otherwise
        """
        try:
            # Try to make a simple request to check connectivity
            await self._make_request('GET', '/health')
            return True
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

    async def submit_resource(self, resource: Resource) -> ResourceSubmissionResponse:
        """
        Submit a single resource to the AI Navigator API.

        Args:
            resource: Resource object to submit

        Returns:
            ResourceSubmissionResponse with submission details

        Raises:
            ValidationError: If resource data is invalid
            APIClientError: If submission fails
        """
        logger.info(f"Submitting resource: {resource.name}")

        try:
            # Convert resource to dict, excluding None values
            resource_data = resource.model_dump(exclude_none=True, by_alias=True)

            # Make the API request
            response_data = await self._make_request(
                method='POST',
                endpoint='/entities',
                data=resource_data,
            )

            # Parse response
            submission_response = ResourceSubmissionResponse(
                success=True,
                message=response_data.get('message', 'Resource submitted successfully'),
                data=response_data,
                entity_id=response_data.get('id'),
                status=response_data.get('status', 'PENDING'),
            )

            logger.info(f"Successfully submitted resource: {resource.name} (ID: {submission_response.entity_id})")
            return submission_response

        except ValidationError:
            logger.error(f"Validation failed for resource: {resource.name}")
            raise

        except APIClientError as e:
            logger.error(f"Failed to submit resource {resource.name}: {e}")
            # Return error response
            return ResourceSubmissionResponse(
                success=False,
                message=str(e),
                errors=[APIError(error="submission_failed", message=str(e))],
            )

        except Exception as e:
            logger.error(f"Unexpected error submitting resource {resource.name}: {e}")
            return ResourceSubmissionResponse(
                success=False,
                message=f"Unexpected error: {e}",
                errors=[APIError(error="unexpected_error", message=str(e))],
            )

    async def submit_resources_batch(
        self,
        resources: List[Resource],
        batch_size: int = 10,
        delay_between_batches: float = 1.0,
    ) -> BatchSubmissionResponse:
        """
        Submit multiple resources in batches.

        Args:
            resources: List of Resource objects to submit
            batch_size: Number of resources to submit per batch
            delay_between_batches: Delay in seconds between batches

        Returns:
            BatchSubmissionResponse with overall results
        """
        logger.info(f"Starting batch submission of {len(resources)} resources")

        total_submitted = 0
        successful_submissions = 0
        failed_submissions = 0
        submission_results: List[ResourceSubmissionResponse] = []

        # Process resources in batches
        for i in range(0, len(resources), batch_size):
            batch = resources[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(resources) + batch_size - 1) // batch_size

            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} resources)")

            # Submit each resource in the batch
            batch_tasks = []
            for resource in batch:
                task = self.submit_resource(resource)
                batch_tasks.append(task)

            # Wait for all submissions in this batch to complete
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # Process batch results
            for result in batch_results:
                total_submitted += 1

                if isinstance(result, Exception):
                    logger.error(f"Batch submission exception: {result}")
                    failed_submissions += 1
                    submission_results.append(
                        ResourceSubmissionResponse(
                            success=False,
                            message=str(result),
                            errors=[APIError(error="batch_exception", message=str(result))],
                        )
                    )
                elif isinstance(result, ResourceSubmissionResponse):
                    submission_results.append(result)
                    if result.success:
                        successful_submissions += 1
                    else:
                        failed_submissions += 1
                else:
                    logger.error(f"Unexpected result type: {type(result)}")
                    failed_submissions += 1

            # Delay between batches (except for the last batch)
            if i + batch_size < len(resources):
                logger.debug(f"Waiting {delay_between_batches}s before next batch")
                await asyncio.sleep(delay_between_batches)

        # Create overall response
        batch_response = BatchSubmissionResponse(
            success=failed_submissions == 0,
            message=f"Batch submission completed: {successful_submissions}/{total_submitted} successful",
            total_submitted=total_submitted,
            successful_submissions=successful_submissions,
            failed_submissions=failed_submissions,
            submission_results=submission_results,
        )

        logger.info(
            f"Batch submission completed: {successful_submissions} successful, "
            f"{failed_submissions} failed out of {total_submitted} total"
        )

        return batch_response

    async def get_submission_status(self, entity_id: str) -> Dict[str, Any]:
        """
        Get the status of a submitted resource.

        Args:
            entity_id: ID of the submitted entity

        Returns:
            Entity status information
        """
        logger.debug(f"Getting status for entity: {entity_id}")

        try:
            response_data = await self._make_request(
                method='GET',
                endpoint=f'/entities/{entity_id}',
            )
            return response_data

        except APIClientError as e:
            logger.error(f"Failed to get status for entity {entity_id}: {e}")
            raise

    async def update_resource(self, entity_id: str, updates: Dict[str, Any]) -> ResourceSubmissionResponse:
        """
        Update an existing resource.

        Args:
            entity_id: ID of the entity to update
            updates: Dictionary of fields to update

        Returns:
            ResourceSubmissionResponse with update results
        """
        logger.info(f"Updating entity: {entity_id}")

        try:
            response_data = await self._make_request(
                method='PATCH',
                endpoint=f'/entities/{entity_id}',
                data=updates,
            )

            return ResourceSubmissionResponse(
                success=True,
                message="Resource updated successfully",
                data=response_data,
                entity_id=entity_id,
                status=response_data.get('status'),
            )

        except APIClientError as e:
            logger.error(f"Failed to update entity {entity_id}: {e}")
            return ResourceSubmissionResponse(
                success=False,
                message=str(e),
                errors=[APIError(error="update_failed", message=str(e))],
            )
