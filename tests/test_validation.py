"""
Unit tests for the API response validation module.
Tests validation logic for API responses, resource data, and error handling.
"""

import pytest
from uuid import uuid4, UUID

from arep.api.validation import ResponseValidator, ResponseValidationError
from arep.api.models import Resource, ResourceSubmissionResponse, APIError


class TestResponseValidator:
    """Test cases for the ResponseValidator class."""
    
    def test_validate_response_structure_valid(self):
        """Test validation of valid response structure."""
        response_data = {
            "success": True,
            "message": "Operation successful",
            "data": {"id": str(uuid4())}
        }
        
        result = ResponseValidator.validate_response_structure(response_data)
        assert result is True
    
    def test_validate_response_structure_invalid_type(self):
        """Test validation fails for non-dict response."""
        with pytest.raises(ResponseValidationError, match="Response must be a dictionary"):
            ResponseValidator.validate_response_structure("invalid")
    
    def test_validate_response_structure_error_without_message(self):
        """Test validation warns for error response without message."""
        response_data = {"error": "Something went wrong"}
        
        # Should not raise exception but may log warning
        result = ResponseValidator.validate_response_structure(response_data)
        assert result is True
    
    def test_validate_entity_id_valid_string(self):
        """Test validation of valid UUID string."""
        test_uuid = str(uuid4())
        result = ResponseValidator.validate_entity_id(test_uuid)
        
        assert isinstance(result, UUID)
        assert str(result) == test_uuid
    
    def test_validate_entity_id_valid_uuid(self):
        """Test validation of valid UUID object."""
        test_uuid = uuid4()
        result = ResponseValidator.validate_entity_id(test_uuid)
        
        assert isinstance(result, UUID)
        assert result == test_uuid
    
    def test_validate_entity_id_none(self):
        """Test validation fails for None entity ID."""
        with pytest.raises(ResponseValidationError, match="Entity ID cannot be None"):
            ResponseValidator.validate_entity_id(None)
    
    def test_validate_entity_id_invalid_string(self):
        """Test validation fails for invalid UUID string."""
        with pytest.raises(ResponseValidationError, match="Invalid UUID format"):
            ResponseValidator.validate_entity_id("not-a-uuid")
    
    def test_validate_entity_id_invalid_type(self):
        """Test validation fails for invalid type."""
        with pytest.raises(ResponseValidationError, match="Invalid entity ID type"):
            ResponseValidator.validate_entity_id(123)
    
    def test_validate_status_valid(self):
        """Test validation of valid status."""
        result = ResponseValidator.validate_status("PENDING")
        assert result == "PENDING"
    
    def test_validate_status_invalid_type(self):
        """Test validation fails for non-string status."""
        with pytest.raises(ResponseValidationError, match="Status must be a string"):
            ResponseValidator.validate_status(123)
    
    def test_validate_status_unknown_status(self):
        """Test validation accepts unknown status with warning."""
        # Should not raise exception but may log warning
        result = ResponseValidator.validate_status("UNKNOWN_STATUS")
        assert result == "UNKNOWN_STATUS"
    
    def test_validate_url_valid(self):
        """Test validation of valid URL."""
        url = "https://example.com/path"
        result = ResponseValidator.validate_url(url)
        assert result == url
    
    def test_validate_url_none(self):
        """Test validation accepts None URL."""
        result = ResponseValidator.validate_url(None)
        assert result is None
    
    def test_validate_url_invalid_type(self):
        """Test validation fails for non-string URL."""
        with pytest.raises(ResponseValidationError, match="URL must be a string"):
            ResponseValidator.validate_url(123)
    
    def test_validate_url_invalid_format(self):
        """Test validation fails for invalid URL format."""
        with pytest.raises(ResponseValidationError, match="Invalid URL format"):
            ResponseValidator.validate_url("not-a-url")
    
    def test_validate_url_with_field_name(self):
        """Test validation with custom field name."""
        with pytest.raises(ResponseValidationError, match="Invalid logo_url format"):
            ResponseValidator.validate_url("invalid", "logo_url")
    
    def test_extract_errors_single_error_string(self):
        """Test extracting single error as string."""
        response_data = {"error": "Something went wrong"}
        errors = ResponseValidator.extract_errors(response_data)
        
        assert len(errors) == 1
        assert errors[0].error == "api_error"
        assert errors[0].message == "Something went wrong"
    
    def test_extract_errors_single_error_dict(self):
        """Test extracting single error as dict."""
        response_data = {
            "error": {
                "type": "validation_error",
                "message": "Invalid input",
                "details": {"field": "name"}
            }
        }
        errors = ResponseValidator.extract_errors(response_data)
        
        assert len(errors) == 1
        assert errors[0].error == "validation_error"
        assert errors[0].message == "Invalid input"
        assert errors[0].details == {"field": "name"}
    
    def test_extract_errors_multiple_errors(self):
        """Test extracting multiple errors."""
        response_data = {
            "errors": [
                "First error",
                {"type": "custom_error", "message": "Second error"}
            ]
        }
        errors = ResponseValidator.extract_errors(response_data)
        
        assert len(errors) == 2
        assert errors[0].error == "validation_error"
        assert errors[0].message == "First error"
        assert errors[1].error == "custom_error"
        assert errors[1].message == "Second error"
    
    def test_extract_errors_validation_detail(self):
        """Test extracting validation errors from detail field."""
        response_data = {
            "detail": [
                {
                    "loc": ["name"],
                    "msg": "field required",
                    "type": "value_error.missing"
                },
                {
                    "loc": ["website_url"],
                    "msg": "invalid URL format",
                    "type": "value_error.url"
                }
            ]
        }
        errors = ResponseValidator.extract_errors(response_data)
        
        assert len(errors) == 2
        assert errors[0].error == "validation_error"
        assert "name: field required" in errors[0].message
        assert errors[1].error == "validation_error"
        assert "website_url: invalid URL format" in errors[1].message
    
    def test_extract_errors_validation_detail_string(self):
        """Test extracting validation error from detail string."""
        response_data = {"detail": "Validation failed"}
        errors = ResponseValidator.extract_errors(response_data)
        
        assert len(errors) == 1
        assert errors[0].error == "validation_error"
        assert errors[0].message == "Validation failed"
    
    def test_extract_errors_no_errors(self):
        """Test extracting errors when none present."""
        response_data = {"success": True, "data": {}}
        errors = ResponseValidator.extract_errors(response_data)
        
        assert len(errors) == 0
    
    def test_validate_submission_response_success(self):
        """Test validation of successful submission response."""
        entity_id = uuid4()
        response_data = {
            "success": True,
            "message": "Resource created successfully",
            "id": str(entity_id),
            "status": "PENDING"
        }
        
        result = ResponseValidator.validate_submission_response(response_data)
        
        assert isinstance(result, ResourceSubmissionResponse)
        assert result.success is True
        assert result.message == "Resource created successfully"
        assert result.entity_id == entity_id
        assert result.status == "PENDING"
        assert result.errors is None
    
    def test_validate_submission_response_with_errors(self):
        """Test validation of submission response with errors."""
        response_data = {
            "success": False,
            "error": "Validation failed",
            "message": "Resource creation failed"
        }
        
        result = ResponseValidator.validate_submission_response(response_data)
        
        assert isinstance(result, ResourceSubmissionResponse)
        assert result.success is False
        assert result.message == "Resource creation failed"
        assert result.errors is not None
        assert len(result.errors) == 1
        assert result.errors[0].message == "Validation failed"
    
    def test_validate_submission_response_minimal(self):
        """Test validation of minimal submission response."""
        response_data = {}
        
        result = ResponseValidator.validate_submission_response(response_data)
        
        assert isinstance(result, ResourceSubmissionResponse)
        assert result.success is True  # Default assumption
        assert result.message == "Resource submitted successfully"  # Default message
        assert result.entity_id is None
        assert result.status is None
    
    def test_validate_submission_response_with_name_mismatch(self):
        """Test validation logs warning for name mismatch."""
        response_data = {
            "name": "Different Name",
            "id": str(uuid4())
        }
        
        # Should not raise exception but may log warning
        result = ResponseValidator.validate_submission_response(
            response_data, 
            expected_resource_name="Expected Name"
        )
        
        assert isinstance(result, ResourceSubmissionResponse)
    
    def test_validate_resource_data_valid(self):
        """Test validation of valid resource data."""
        resource = Resource(
            name="Test AI Tool",
            website_url="https://example.com",
            entity_type_id=uuid4(),
            short_description="A comprehensive test AI tool for validation testing",
        )
        
        errors = ResponseValidator.validate_resource_data(resource)
        assert len(errors) == 0
    
    def test_validate_resource_data_short_name(self):
        """Test validation fails for short name."""
        resource = Resource(
            name="AI",  # Too short
            website_url="https://example.com",
            entity_type_id=uuid4(),
            short_description="A comprehensive test AI tool",
        )
        
        errors = ResponseValidator.validate_resource_data(resource)
        assert len(errors) > 0
        assert any("Must be at least 3 characters long" in error for error in errors)
    
    def test_validate_resource_data_short_description(self):
        """Test validation fails for short description."""
        resource = Resource(
            name="Test AI Tool",
            website_url="https://example.com",
            entity_type_id=uuid4(),
            short_description="Short",  # Too short
        )
        
        errors = ResponseValidator.validate_resource_data(resource)
        assert len(errors) > 0
        assert any("Must be at least 10 characters long" in error for error in errors)
    
    def test_validate_resource_data_invalid_url(self):
        """Test validation fails for invalid URL."""
        # Since Pydantic validates URLs during model creation,
        # we need to test this differently
        from pydantic import ValidationError

        with pytest.raises(ValidationError) as exc_info:
            Resource(
                name="Test AI Tool",
                website_url="not-a-url",  # Invalid URL
                entity_type_id=uuid4(),
                short_description="A comprehensive test AI tool",
            )

        # Check that the error is about URL validation
        assert "url_parsing" in str(exc_info.value)
    
    def test_sanitize_response_data(self):
        """Test sanitization of response data."""
        response_data = {
            "id": str(uuid4()),
            "name": "Test Resource",
            "api_key": "secret-key-123",
            "token": "secret-token-456",
            "password": "secret-password",
            "normal_field": "normal_value"
        }
        
        sanitized = ResponseValidator.sanitize_response_data(response_data)
        
        assert sanitized["id"] == response_data["id"]
        assert sanitized["name"] == response_data["name"]
        assert sanitized["normal_field"] == response_data["normal_field"]
        assert sanitized["api_key"] == "[REDACTED]"
        assert sanitized["token"] == "[REDACTED]"
        assert sanitized["password"] == "[REDACTED]"
    
    def test_sanitize_response_data_invalid_uuid(self):
        """Test sanitization logs warning for invalid UUID."""
        response_data = {
            "id": "not-a-uuid",
            "name": "Test Resource"
        }
        
        # Should not raise exception but may log warning
        sanitized = ResponseValidator.sanitize_response_data(response_data)
        assert sanitized["id"] == "not-a-uuid"
        assert sanitized["name"] == "Test Resource"


@pytest.fixture
def sample_resource():
    """Create a sample resource for testing."""
    return Resource(
        name="Test AI Tool",
        website_url="https://example.com",
        entity_type_id=uuid4(),
        short_description="A comprehensive test AI tool for validation testing",
        description="This is a longer description of the test AI tool with more details.",
    )


def test_response_validation_integration(sample_resource):
    """Integration test for response validation workflow."""
    # Simulate a complete validation workflow
    
    # 1. Validate resource data before submission
    validation_errors = ResponseValidator.validate_resource_data(sample_resource)
    assert len(validation_errors) == 0
    
    # 2. Simulate API response
    entity_id = uuid4()
    api_response = {
        "success": True,
        "message": "Resource created successfully",
        "id": str(entity_id),
        "status": "PENDING",
        "name": sample_resource.name
    }
    
    # 3. Validate submission response
    validated_response = ResponseValidator.validate_submission_response(
        api_response, 
        expected_resource_name=sample_resource.name
    )
    
    assert validated_response.success is True
    assert validated_response.entity_id == entity_id
    assert validated_response.status == "PENDING"
    
    # 4. Sanitize response data
    sanitized = ResponseValidator.sanitize_response_data(api_response)
    assert sanitized["id"] == str(entity_id)
    assert sanitized["name"] == sample_resource.name
